// Services
const generalDocumentService = require('../services/general-qhsc-document.service');

// Utils
const { errorResponse, successResponse } = require('../utils/response.utils');
const HTTP_STATUS = require('../utils/status-codes');
const constants = require('../utils/constants.utils');
const commonUtils = require('../utils/common.utils');
const { validateSearch } = require('../utils/common-function.utils');
const commonfunctionUtils = require('../utils/common-function.utils');

/**
 * Get all documents
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllDocuments = async (req, res) => {
  try {
    const search = req.query.search ? await validateSearch(req.query.search) : null;
    const page = req.query.page ? parseInt(req.query.page) : 0;
    const perPage = req.query.perPage ? parseInt(req.query.perPage) : 10;
    const sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    const sortBy = req.query.sortBy ? { [req.query.sortBy]: sort } : { createdAt: sort };

    const filter = {
      account: req.userData.account,
      deletedAt: null,
      ...(search && { documentName: { $regex: search, $options: 'i' } }),
    };

    const response = await generalDocumentService.getAllDocuments(filter, page, perPage, sortBy);

    res.status(HTTP_STATUS.OK).json(successResponse(constants.GET_GENERAL_DOCUMENT, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

/**
 * Create document
 *
 * @param {*} req
 * @param {*} res
 */
exports.createDocuments = async (req, res) => {
  try {
    const response = await generalDocumentService.createDocument({
      ...req.body,
      account: req.userData.account,
    });

    // update sync api manage data
    if (response) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    res.status(HTTP_STATUS.OK).json(successResponse(constants.GET_GENERAL_DOCUMENT, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

/**
 * Create document
 *
 * @param {*} req
 * @param {*} res
 */
exports.getDocument = async (req, res) => {
  try {
    if (!commonUtils.isValidId(req.params.id)) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(errorResponse(constants.INVALID_ID));
    }

    const filter = {
      _id: commonUtils.toObjectId(req.params.id),
      account: req.userData.account,
      deletedAt: null,
    };

    const exist = await generalDocumentService.getDocument(filter);

    if (!exist) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(errorResponse(constants.NO_GENERAL_DOCUMENT));
    }

    const response = await generalDocumentService.getDocument(filter);

    res.status(HTTP_STATUS.OK).json(successResponse(constants.GET_GENERAL_DOCUMENT, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

/**
 * Update document
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateDocument = async (req, res) => {
  try {
    if (!commonUtils.isValidId(req.params.id)) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(errorResponse(constants.INVALID_ID));
    }

    const filter = {
      _id: commonUtils.toObjectId(req.params.id),
      account: req.userData.account,
      deletedAt: null,
    };

    const exist = await generalDocumentService.getDocument(filter);

    if (!exist) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(errorResponse(constants.NO_GENERAL_DOCUMENT));
    }

    await generalDocumentService.updateDocument(commonUtils.toObjectId(req.params.id), req.body);

    // update sync api manage data
    await this.commonUpdateSyncApiManage(req.userData.account);

    res.status(HTTP_STATUS.OK).json(successResponse(constants.GET_GENERAL_DOCUMENT));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

/**
 * Delete document
 *
 * @param {*} req
 * @param {*} res
 */
exports.deleteDocument = async (req, res) => {
  try {
    if (!commonUtils.isValidId(req.params.id)) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(errorResponse(constants.INVALID_ID));
    }

    const filter = {
      _id: commonUtils.toObjectId(req.params.id),
      account: req.userData.account,
      deletedAt: null,
    };

    const exist = await generalDocumentService.getDocument(filter);

    if (!exist) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(errorResponse(constants.NO_GENERAL_DOCUMENT));
    }

    const response = await generalDocumentService.deleteDocument(req.params.id, req.deletedAt);

    if (response) {
      // update sync api manage data
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    res.status(HTTP_STATUS.OK).json(successResponse(constants.DOCUMENT_DELETED, response));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error.message));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonfunctionUtils.updateSyncApiManage({
    syncApis: ['projectDocuments'],
    account,
  });
};
