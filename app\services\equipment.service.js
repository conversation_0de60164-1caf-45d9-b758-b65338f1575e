// models
const Equipment = require('../models/equipment.model');
const EquipmentOrderHistory = require('../models/equipment-order-history.model');
const InventoryHistory = require('../models/inventory-history.model');
const EquipmentCategory = require('../models/equipment-category.model');

// services
const equipmentTypeService = require('../services/equipment-type.service');
const inventoryHistoryService = require('../services/inventory-history.service');

/**
 * Create Equipment
 *
 * @param {*} requestData
 * @returns
 */
exports.createEquipment = async requestData => {
  return await Equipment.create(requestData);
};

/**
 * Filter Equipments
 *
 * @param {*} filter
 * @param {*} perPage
 * @param {*} page
 * @param {*} sort
 * @returns
 */
exports.getEquipments = async (filter, page, perPage, sort) => {
  return await Equipment.find(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  })
    .collation({ locale: 'en' })
    .sort(sort)
    .limit(perPage)
    .skip(page * perPage)
    .populate(this.populateEquipmentFields());
};

/**
 * Get Equipment by Filter
 *
 * @param {*} id
 * @returns
 */
exports.getSingleEquipmentByFilter = async filter => {
  filter.deletedAt = null;
  filter.isActive = true;
  return await Equipment.findOne(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  }).populate(this.populateEquipmentFields());
};

/**
 * Update Equipment
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateEquipment = async (id, requestData, session) => {
  return await Equipment.findByIdAndUpdate(id, { $set: requestData }, { new: true, session });
};

/**
 * Delete Equipment
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteEquipment = async (id, deletedAt) => {
  return await Equipment.findByIdAndUpdate(id, { $set: deletedAt }, { new: true });
};

/**
 * Populate Equipment Fields
 *
 * @returns
 */
exports.populateEquipmentFields = () => {
  return [
    {
      path: 'equipmentType',
      model: 'equipment-type',
      select: {
        _id: 1,
        type: 1,
        equipmentCategory: 1,
        equipmentUnit: 1,
        currencyUnit: 1,
        price: 1,
        quantityType: 1,
        hsCode: 1,
      },
      populate: [
        {
          path: 'equipmentCategory',
          model: 'equipment-category',
          select: { _id: 1, name: 1, abbreviation: 1 },
        },
        {
          path: 'equipmentUnit',
          model: 'equipment-unit',
          select: { _id: 1, title: 1, abbreviation: 1 },
        },
        {
          path: 'currencyUnit',
          model: 'currency-unit',
          select: { _id: 1, name: 1, symbol: 1 },
        },
        {
          path: 'quantityType',
          model: 'equipment-quantity-type',
          select: { _id: 1, name: 1, priceType: 1, quantityType: 1 },
        },
        {
          path: 'hsCode',
          model: 'hs-code',
          select: { _id: 1, name: 1, code: 1 },
        },
      ],
    },
    {
      path: 'certificateType',
      model: 'equipment-certificate-type',
      select: { _id: 1, title: 1 },
    },
    {
      path: 'warehouse',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'account',
      select: { _id: 1, name: 1 },
      strictPopulate: false,
    },
    {
      path: 'createdBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'updatedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
    {
      path: 'deletedBy',
      model: 'user',
      select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
    },
  ];
};

/**
 * Auto generate equipment number
 *
 * @param {*} equipmentType
 * @param {*} account
 * @returns
 */
exports.equipmentNumberGenerator = async (equipmentType, account) => {
  try {
    const equipType = await equipmentTypeService.getEquipmentTypeById(equipmentType);
    const [equipCategory] = await equipmentTypeService.getEquipmentType(
      {
        _id: equipmentType,
        account: account,
        deletedAt: null,
      },
      0,
      1,
      -1
    );

    let equipTypeData = await equipmentTypeService.getEquipmentType(
      {
        equipmentCategory: equipCategory.equipmentCategory._id,
        account: account,
        deletedAt: null,
      },
      0,
      100,
      -1
    );
    let Ids = equipTypeData.map(equipmentType => equipmentType._id);

    let filterData = {
      equipmentType: { $in: Ids },
      account: account,
    };
    const equipment = await this.getEquipments(filterData, 0, 1, { updatedAt: -1 });

    let newEquipmentNumber = `${equipType.equipmentCategory.abbreviation}${global.constant.DEFAULT_POST_NUMBER_FORMAT}`;
    if (equipment[0]?.equipmentNumber) {
      let existingNumber = equipment[0].equipmentNumber;
      existingNumber = existingNumber.slice(3, 8);
      existingNumber = parseInt(existingNumber) + 1;
      existingNumber = existingNumber.toString().padStart(5, '0');
      newEquipmentNumber = `${equipType.equipmentCategory.abbreviation}${existingNumber}`;
    }
    return newEquipmentNumber;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * Check the equipment value
 *
 * @param {*} equipmentTypeId
 * @returns
 */
exports.checkEquipmentValue = async equipmentTypeId => {
  try {
    const equipmentType = await equipmentTypeService.getEquipmentTypeById(equipmentTypeId);
    return equipmentType.price;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * Equipment Search By name, type or category
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 * @returns
 */
exports.equipmentSearchByKeyword = async (defaultFilter, page, perPage, sort, filter = {}) => {
  let aggregateFilter = [];
  if (Object.keys(filter).length === 0 && filter.constructor === Object) {
    aggregateFilter = [
      {
        $match: defaultFilter,
      },
      {
        $sort: sort,
      },
    ];
    if (page !== '' && perPage !== '') {
      aggregateFilter.push(
        {
          $skip: parseInt(page) * parseInt(perPage),
        },
        {
          $limit: parseInt(perPage),
        }
      );
    }
    aggregateFilter.push(
      {
        $lookup: {
          from: 'equipment-types',
          localField: 'equipmentType',
          foreignField: '_id',
          pipeline: [
            {
              $project: {
                _id: 1,
                type: 1,
                equipmentCategory: 1,
                equipmentUnit: 1,
                currencyUnit: 1,
                price: 1,
                quantityType: 1,
                hsCode: 1,
                certificateTypes: 1,
                isTemporary: { $ifNull: ['$isTemporary', false] },
              },
            },
            {
              $lookup: {
                from: 'equipment-categories',
                localField: 'equipmentCategory',
                foreignField: '_id',
                pipeline: [{ $project: { _id: 1, name: 1, abbreviation: 1 } }],
                as: 'equipmentCategory',
              },
            },
            {
              $lookup: {
                from: 'equipment-units',
                localField: 'equipmentUnit',
                foreignField: '_id',
                pipeline: [{ $project: { _id: 1, title: 1, abbreviation: 1 } }],
                as: 'equipmentUnit',
              },
            },
            {
              $lookup: {
                from: 'currency-units',
                localField: 'currencyUnit',
                foreignField: '_id',
                pipeline: [{ $project: { _id: 1, name: 1, symbol: 1 } }],
                as: 'currencyUnit',
              },
            },
            {
              $lookup: {
                from: 'equipment-quantity-types',
                localField: 'quantityType',
                foreignField: '_id',
                pipeline: [{ $project: { _id: 1, name: 1, priceType: 1, quantityType: 1 } }],
                as: 'quantityType',
              },
            },
            {
              $lookup: {
                from: 'hs-codes',
                localField: 'hsCode',
                foreignField: '_id',
                pipeline: [{ $project: { _id: 1, name: 1, code: 1 } }],
                as: 'hsCode',
              },
            },
          ],
          as: 'equipmentType',
        },
      },
      {
        $lookup: {
          from: 'warehouses',
          localField: 'warehouse',
          foreignField: '_id',
          pipeline: [{ $project: { _id: 1, name: 1 } }],
          as: 'warehouse',
        },
      },
      {
        $unwind: '$warehouse',
      },
      {
        $unwind: { path: '$equipmentType', preserveNullAndEmptyArrays: true },
      },
      {
        $addFields: {
          equipmentType: { $ifNull: ['$equipmentType', {}] },
        },
      },
      {
        $unset: ['deletedBy', 'deletedAt', 'isDeleted', '__v'],
      }
    );
  } else {
    aggregateFilter = [
      {
        $match: defaultFilter,
      },
      {
        $lookup: {
          from: 'equipment-types',
          localField: 'equipmentType',
          foreignField: '_id',
          pipeline: [
            {
              $project: {
                _id: 1,
                type: 1,
                equipmentCategory: 1,
                equipmentUnit: 1,
                currencyUnit: 1,
                price: 1,
                quantityType: 1,
                hsCode: 1,
                certificateTypes: 1,
                isTemporary: { $ifNull: ['$isTemporary', false] },
              },
            },
            {
              $lookup: {
                from: 'equipment-categories',
                localField: 'equipmentCategory',
                foreignField: '_id',
                pipeline: [{ $project: { _id: 1, name: 1, abbreviation: 1 } }],
                as: 'equipmentCategory',
              },
            },
            {
              $lookup: {
                from: 'equipment-units',
                localField: 'equipmentUnit',
                foreignField: '_id',
                pipeline: [{ $project: { _id: 1, title: 1, abbreviation: 1 } }],
                as: 'equipmentUnit',
              },
            },
            {
              $lookup: {
                from: 'currency-units',
                localField: 'currencyUnit',
                foreignField: '_id',
                pipeline: [{ $project: { _id: 1, name: 1, symbol: 1 } }],
                as: 'currencyUnit',
              },
            },
            {
              $lookup: {
                from: 'equipment-quantity-types',
                localField: 'quantityType',
                foreignField: '_id',
                pipeline: [{ $project: { _id: 1, name: 1, priceType: 1, quantityType: 1 } }],
                as: 'quantityType',
              },
            },
            {
              $lookup: {
                from: 'hs-codes',
                localField: 'hsCode',
                foreignField: '_id',
                pipeline: [{ $project: { _id: 1, name: 1, code: 1 } }],
                as: 'hsCode',
              },
            },
          ],
          as: 'equipmentType',
        },
      },
      {
        $lookup: {
          from: 'warehouses',
          localField: 'warehouse',
          foreignField: '_id',
          pipeline: [{ $project: { _id: 1, name: 1 } }],
          as: 'warehouse',
        },
      },
      {
        $unwind: '$warehouse',
      },
      {
        $unwind: { path: '$equipmentType', preserveNullAndEmptyArrays: true },
      },
      {
        $addFields: {
          equipmentType: { $ifNull: ['$equipmentType', {}] },
        },
      },
      {
        $unset: ['deletedBy', 'deletedAt', 'isDeleted', '__v'],
      },
      {
        $match: filter,
      },
      {
        $sort: sort,
      },
      {
        $facet: {
          metadata: [{ $count: 'totalDocuments' }],
          data: [
            ...(page !== '' && perPage !== ''
              ? [{ $skip: parseInt(page) * parseInt(perPage) }, { $limit: parseInt(perPage) }]
              : []),
          ],
        },
      },
    ];
  }
  let coll = { collation: { locale: 'en' } };
  return await Equipment.aggregate(aggregateFilter, coll);
};

/**
 * Bind QR Code
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.bindQRCode = async (id, requestData) => {
  let getQR = await Equipment.findById(id, { qrCode: 1 });
  if (getQR?.qrCode.length > 0 && getQR.qrCode[0].code === undefined) {
    return this.updateEquipment(id, { qrCode: requestData });
  }
  return Equipment.updateOne({ _id: id }, { $push: { qrCode: requestData } });
};

/**
 * Find QR Code
 *
 * @param {*} qrCodeNumber
 * @returns
 */
exports.findQRCode = async (qrCodeNumber, equipmentType = null) => {
  let filter = {
    ...(equipmentType ? { equipmentType } : {}),
    qrCode: { $elemMatch: { code: qrCodeNumber, isActive: true } },
    deletedAt: null,
  };
  return Equipment.find(filter, { deletedAt: 0, deletedBy: 0, __v: 0 }).populate(
    this.populateEquipmentFields()
  );
};

/**
 * Check QR Code Out Dated
 *
 * @param {*} equipmentData
 * @param {*} qrCode
 * @returns
 */
exports.checkQRCodeOutDated = async (equipmentData, qrCode) => {
  const index = equipmentData[0].qrCode.findIndex(item => item.code === qrCode);
  return index === equipmentData[0].qrCode.length - 1;
};

/**
 * Check Unique QR code
 *
 * @param {*} qrCode
 */
exports.checkUniqueQrCode = async filter => {
  return await Equipment.findOne(filter);
};

/**
 * Generate Product Number (New Logic)
 *
 * @param {*} equipmentType
 * @returns
 */
exports.productNumberGenerator = async equipmentType => {
  const equipType = await equipmentTypeService.getEquipmentTypeById(equipmentType);
  const equipment = await this.getEquipments({}, 0, 1, { createdAt: -1 });

  let newEquipmentNumber = `${equipType.equipmentCategory.abbreviation}${global.constant.DEFAULT_POST_NUMBER_FORMAT}`;
  if (equipment[0]?.equipmentNumber) {
    let existingNumber = await this.makeEquipmentNumber(equipment[0].equipmentNumber);
    newEquipmentNumber = `${equipType.equipmentCategory.abbreviation}${existingNumber}`;
    newEquipmentNumber = await this.recursiveEquipmentNumber(newEquipmentNumber, equipType);
  }
  return newEquipmentNumber;
};

/**
 * Equipment Number Process
 *
 * @param {*} equipmentNumber
 * @returns
 */
exports.makeEquipmentNumber = async equipmentNumber => {
  let equipNumber = equipmentNumber;
  equipNumber = equipNumber.slice(3, 9);
  equipNumber = parseInt(equipNumber) + 1;
  equipNumber = equipNumber.toString().padStart(6, '0');
  return equipNumber;
};

/**
 * Recursive Function For Duplicate Equipment Number
 *
 * @param {*} equipmentNumber
 * @param {*} equipType
 * @returns
 */
exports.recursiveEquipmentNumber = async (equipmentNumber, equipType) => {
  let newEquipmentNumber = equipmentNumber;
  let checkEquipment = await Equipment.find({ equipmentNumber: newEquipmentNumber });
  if (checkEquipment.length > 0) {
    let changeNewEquipmentNumber = await this.makeEquipmentNumber(newEquipmentNumber);
    newEquipmentNumber = `${equipType.equipmentCategory.abbreviation}${changeNewEquipmentNumber}`;
    newEquipmentNumber = await this.recursiveEquipmentNumber(newEquipmentNumber);
  }
  return newEquipmentNumber;
};

/**
 * Check Equipment Location
 *
 * @param {*} requestData
 * @returns
 */
exports.checkEquipmentLocation = async requestData => {
  for (let data of requestData) {
    data.inventoryLocation = '';
    if (data.equipmentType.quantityType[0].quantityType === 'unique') {
      let inventoryHistoryData = await inventoryHistoryService.getInventoryHistoryOneByFilter(
        {
          equipment: data._id,
        },
        -1
      );
      data.inventoryLocation = inventoryHistoryData?.tracker;
    }
  }
  return requestData;
};

/**
 * Check Multiple Type Equipment Detail
 *
 * @param {*} equipmentId
 * @returns
 */
exports.checkMultipleTypeEquipmentDetail = async equipmentId => {
  // Get Equipment Order History Data
  let historyData = await EquipmentOrderHistory.aggregate([
    {
      $match: {
        equipment: equipmentId,
        returnOrder: { $eq: [] },
        deletedAt: null,
      },
    },
    {
      $group: {
        _id: '$pmOrder',
        quantity: { $sum: '$wmDispatchQuantity' },
      },
    },
    {
      $lookup: {
        from: 'pm-orders',
        localField: '_id',
        foreignField: '_id',
        pipeline: [
          {
            $match: { deletedAt: null },
          },
          {
            $project: {
              _id: 1,
              orderNumber: 1,
              project: 1,
            },
          },
        ],
        as: 'pmOrder',
      },
    },
    {
      $unwind: '$pmOrder',
    },
    {
      $group: {
        _id: '$pmOrder.project',
        pmOrderData: {
          $push: {
            orderId: '$pmOrder._id',
            orderNumber: '$pmOrder.orderNumber',
          },
        },
      },
    },
  ]);

  // Prepare Inventory Tracker Data
  let prepareInventoryData = {
    totalQuantity: 0,
    data: [],
  };
  prepareInventoryData = await this.getTotalQuanityFromInventoryHistory(
    equipmentId,
    prepareInventoryData
  );

  if (historyData.length > 0) {
    for (let data of historyData) {
      let inventoryInnerData = {
        project: data._id,
      };
      let calculateQunatity = 0;
      let inventoryTracker = '';
      for (let pmOrder of data.pmOrderData) {
        let inventoryData = await inventoryHistoryService.getInventoryHistoryOneByFilter(
          {
            equipment: equipmentId,
            pmOrder: pmOrder.orderId,
          },
          -1
        );
        calculateQunatity += inventoryData.quantity;
        inventoryTracker = inventoryData.tracker;
      }
      inventoryInnerData.quantity = calculateQunatity;
      inventoryInnerData.tracker = inventoryTracker;
      prepareInventoryData.data.push(inventoryInnerData);
    }
  } else {
    await this.getTotalQuanityFromInventoryHistory(equipmentId, prepareInventoryData, 'tracker');
  }
  return prepareInventoryData;
};

/**
 * Get Total Quantity From Inventory History
 *
 * @param {*} equipmentId
 * @param {*} inventoryData
 * @param {*} type
 * @returns
 */
exports.getTotalQuanityFromInventoryHistory = async (equipmentId, inventoryData, type = null) => {
  let inventoryHistoryLastPurchase = await InventoryHistory.findOne({
    equipment: equipmentId,
    type: 'purchase',
    deletedAt: null,
  }).sort({ createdAt: -1 });

  if (type === 'tracker') {
    return (inventoryData.data = [
      {
        project: '',
        quantity: inventoryHistoryLastPurchase?.quantity,
        tracker: inventoryHistoryLastPurchase?.tracker,
      },
    ]);
  }

  inventoryData.totalQuantity = inventoryHistoryLastPurchase?.quantity || 0;

  let removeQunaity = await InventoryHistory.find({
    equipment: equipmentId,
    type: 'cancel',
    createdAt: {
      $gt: inventoryHistoryLastPurchase?.createdAt,
    },
    deletedAt: null,
  });

  if (removeQunaity.length > 0) {
    for (let data of removeQunaity) {
      inventoryData.totalQuantity = inventoryData.totalQuantity - data.quantity;
    }
  }
  return inventoryData;
};

/**
 * Get equipment summary
 *
 * @param {String} account
 * @param {String} projectId
 * @param {String} dprDate
 * @returns
 */
exports.getEquipmentSummary = async (account, projectId, dprDate, equipmentTypeIds) => {
  const dateOnly = new Date(dprDate).toISOString().split('T')[0];

  const pipeline = [
    {
      $match: {
        account: account,
        deletedAt: null,
        createdAt: {
          $gte: new Date(`${dateOnly}T00:00:00.000Z`),
          $lt: new Date(`${dateOnly}T23:59:59.999Z`),
        },
      },
    },
    {
      $project: {
        _id: 1,
        account: 1,
        equipment: 1,
        equipmentType: 1,
        pmOrder: 1,
      },
    },
    {
      $lookup: {
        from: 'equipment',
        localField: 'equipment',
        foreignField: '_id',
        as: 'equipmentDetails',
        pipeline: [
          { $match: { deletedAt: null } },
          {
            $project: {
              _id: 1,
              name: 1,
              equipmentNumber: 1,
              serialNumber: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentDetails',
    },
    {
      $lookup: {
        from: 'equipment-types',
        localField: 'equipmentType',
        foreignField: '_id',
        as: 'equipmentTypeDetails',
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
          {
            $project: {
              _id: 1,
              type: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentTypeDetails',
    },
    {
      $match: {
        'equipmentTypeDetails._id': { $in: equipmentTypeIds },
      },
    },
    {
      $lookup: {
        from: 'pm-orders',
        localField: 'pmOrder',
        foreignField: '_id',
        as: 'pmOrderDetails',
        pipeline: [
          {
            $match: {
              project: projectId,
              status: { $in: ['partially-check-in', 'check-in'] },
              deletedAt: null,
            },
          },
          {
            $project: {
              _id: 1,
              orderNumber: 1,
              status: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$pmOrderDetails',
    },
    {
      $group: {
        _id: '$_id',
        equipmentType: {
          $first: '$equipmentTypeDetails.type',
        },
        equipmentName: {
          $first: '$equipmentDetails.name',
        },
        equipmentNumber: {
          $first: '$equipmentDetails.equipmentNumber',
        },
        serialNumber: {
          $first: '$equipmentDetails.serialNumber',
        },
        orderNo: {
          $first: '$pmOrderDetails.orderNumber',
        },
      },
    },
  ];
  return await EquipmentOrderHistory.aggregate(pipeline);
};

exports.getEquipmentSummaryView = async (
  filter,
  page,
  perPage,
  sort,
  type,
  priceType,
  quantityType
) => {
  // Build equipmentTypes lookup pipeline
  const equipmentTypesPipeline = [
    { $match: { deletedAt: null } },
    ...(type ? [{ $match: { type: { $regex: type, $options: 'i' } } }] : []),
    {
      $project: {
        _id: 1,
        type: 1,
        equipmentCategory: 1,
        quantityType: 1,
      },
    },
  ];

  // Build equipmentQuantityTypes lookup pipeline
  const equipmentQuantityTypesPipeline = [
    { $match: { deletedAt: null } },
    ...(priceType ? [{ $match: { priceType: { $regex: priceType, $options: 'i' } } }] : []),
    ...(quantityType
      ? [{ $match: { quantityType: { $regex: quantityType, $options: 'i' } } }]
      : []),
  ];

  // Main aggregation pipeline (without pagination and sort)
  const basePipeline = [
    { $match: filter },
    { $project: { _id: 1, name: 1 } },
    {
      $lookup: {
        from: 'equipment-types',
        localField: '_id',
        foreignField: 'equipmentCategory',
        as: 'equipmentTypes',
        pipeline: equipmentTypesPipeline,
      },
    },
    { $unwind: '$equipmentTypes' },
    ...(priceType || quantityType
      ? [
          {
            $lookup: {
              from: 'equipment-quantity-types',
              localField: 'equipmentTypes.quantityType',
              foreignField: '_id',
              as: 'equipmentQuantityTypes',
              pipeline: equipmentQuantityTypesPipeline,
            },
          },
          { $unwind: '$equipmentQuantityTypes' },
        ]
      : []),
    {
      $lookup: {
        from: 'equipment',
        localField: 'equipmentTypes._id',
        foreignField: 'equipmentType',
        as: 'equipment',
        pipeline: [
          { $match: { deletedAt: null } },
          {
            $project: {
              _id: 1,
              name: 1,
              equipmentType: 1,
              quantity: 1,
              certificateType: 1,
              createdAt: 1,
              condition: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: {
        path: '$equipment',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'equipment-order-histories',
        localField: 'equipment._id',
        foreignField: 'equipment',
        as: 'equipmentOrderHistories',
        pipeline: [
          { $match: { deletedAt: null } },
          {
            $match: {
              returnOrder: {
                $eq: [],
              },
              deletedAt: null,
            },
          },
          {
            $group: {
              _id: '$pmOrder',
              quantity: {
                $sum: '$wmDispatchQuantity',
              },
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'equipment-order-histories',
        localField: 'equipmentTypes._id',
        foreignField: 'equipmentType',
        as: 'pmOrderManageEquipments',
        pipeline: [
          {
            $match: {
              status: 'check-in',
              deletedAt: null,
            },
          },
        ],
      },
    },
    {
      $addFields: {
        pmReceivedQuantitySum: {
          $sum: '$pmOrderManageEquipments.pmReceivedQuantity',
        },
      },
    },
    {
      $addFields: {
        availableStock: {
          $cond: {
            if: {
              $and: [{ $ne: ['$equipment', null] }, { $ne: ['$equipment.condition', 'write-off'] }],
            },
            then: '$equipment.quantity',
            else: 0,
          },
        },
      },
    },
    {
      $addFields: {
        inQuarantine: {
          $cond: {
            if: {
              $and: [
                { $ne: ['$equipment', null] },
                { $eq: ['$equipment.condition', 'quarantine'] },
              ],
            },
            then: '$equipment.quantity',
            else: 0,
          },
        },
      },
    },
    {
      $addFields: {
        equipmentOrderHistoriesSum: {
          $sum: '$equipmentOrderHistories.quantity',
        },
      },
    },
    {
      $addFields: {
        totalStock: {
          $add: [
            {
              $cond: {
                if: {
                  $and: [
                    { $ne: ['$equipment', null] },
                    { $ne: ['$equipment.condition', 'write-off'] },
                  ],
                },
                then: { $ifNull: ['$equipment.quantity', 0] },
                else: 0,
              },
            },
            {
              $ifNull: ['$equipmentOrderHistoriesSum', 0],
            },
          ],
        },
      },
    },
    {
      $addFields: {
        expiredCertificates: {
          $cond: {
            if: { $ne: ['$equipment', null] },
            then: {
              $filter: {
                input: {
                  $ifNull: ['$equipment.certificateType', []],
                },
                as: 'certi',
                cond: {
                  $and: [
                    { $ne: ['$$certi.endDate', null] },
                    {
                      $lt: ['$$certi.endDate', new Date()],
                    },
                  ],
                },
              },
            },
            else: [],
          },
        },
      },
    },
    {
      $addFields: {
        expiredCertificatesCount: {
          $size: '$expiredCertificates',
        },
      },
    },
    {
      $group: {
        _id: {
          equipmentType: '$equipmentTypes.type',
        },
        equipmentCategory: { $first: '$name' },
        totalStock: { $sum: '$totalStock' },
        availableStock: { $sum: '$availableStock' },
        inQuarantine: { $sum: '$inQuarantine' },
        totalExpiredCertificates: {
          $sum: '$expiredCertificatesCount',
        },
        atProject: {
          $sum: '$pmReceivedQuantitySum',
        },
        latestCreatedAt: {
          $max: {
            $cond: {
              if: { $ne: ['$equipment', null] },
              then: '$equipment.createdAt',
              else: null,
            },
          },
        },
      },
    },
    {
      $project: {
        _id: 0,
        equipmentCategory: 1,
        equipmentType: '$_id.equipmentType',
        totalStock: 1,
        availableStock: 1,
        inQuarantine: 1,
        atProject: 1,
        totalExpiredCertificates: 1,
        latestCreatedAt: 1,
      },
    },
  ];

  // Facet for pagination and total count
  const facetPipeline = [
    {
      $facet: {
        data: [
          ...basePipeline,
          { $sort: { latestCreatedAt: sort } },
          { $skip: page * perPage },
          { $limit: perPage },
        ],
        totalCount: [...basePipeline, { $count: 'count' }],
      },
    },
  ];

  try {
    const result = await EquipmentCategory.aggregate(facetPipeline);
    const InventorySummary = result[0].data;
    const totalCount = result[0].totalCount[0]?.count || 0;
    return { InventorySummary, totalCount };
  } catch (err) {
    throw new Error('Error in getEquipmentSummaryView: ' + err.message);
  }
};
